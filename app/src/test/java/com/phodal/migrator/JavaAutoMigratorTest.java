package com.phodal.migrator;

import com.phodal.migrator.core.MigrationOptions;
import com.phodal.migrator.core.MigrationResult;
import com.phodal.migrator.core.ProjectType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * JavaAutoMigrator 单元测试
 */
class JavaAutoMigratorTest {
    
    @TempDir
    Path tempDir;
    
    private Path testProjectPath;
    private MigrationOptions defaultOptions;
    
    @BeforeEach
    void setUp() throws IOException {
        testProjectPath = tempDir.resolve("test-project");
        Files.createDirectories(testProjectPath);
        
        defaultOptions = MigrationOptions.builder()
                .dryRun(true)
                .verbose(false)
                .build();
    }
    
    @Test
    void shouldFailWhenProjectPathNotExists() {
        // Given
        Path nonExistentPath = tempDir.resolve("non-existent");
        
        // When & Then
        assertThrows(Exception.class, () -> {
            JavaAutoMigrator migrator = new JavaAutoMigrator(nonExistentPath, defaultOptions);
            migrator.migrate();
        });
    }
    
    @Test
    void shouldDetectMavenProject() throws IOException {
        // Given
        Files.createFile(testProjectPath.resolve("pom.xml"));
        
        // When
        JavaAutoMigrator migrator = new JavaAutoMigrator(testProjectPath, defaultOptions);
        
        // Then
        assertThat(migrator.getContext().getProjectType()).isEqualTo(ProjectType.MAVEN);
    }
    
    @Test
    void shouldDetectGradleProject() throws IOException {
        // Given
        Files.createFile(testProjectPath.resolve("build.gradle"));
        
        // When
        JavaAutoMigrator migrator = new JavaAutoMigrator(testProjectPath, defaultOptions);
        
        // Then
        assertThat(migrator.getContext().getProjectType()).isEqualTo(ProjectType.GRADLE);
    }
    
    @Test
    void shouldDetectGradleKtsProject() throws IOException {
        // Given
        Files.createFile(testProjectPath.resolve("build.gradle.kts"));
        
        // When
        JavaAutoMigrator migrator = new JavaAutoMigrator(testProjectPath, defaultOptions);
        
        // Then
        assertThat(migrator.getContext().getProjectType()).isEqualTo(ProjectType.GRADLE_KTS);
    }
    
    @Test
    void shouldRunInDryRunMode() throws IOException {
        // Given
        Files.createFile(testProjectPath.resolve("pom.xml"));
        
        MigrationOptions dryRunOptions = MigrationOptions.builder()
                .dryRun(true)
                .build();
        
        // When
        JavaAutoMigrator migrator = new JavaAutoMigrator(testProjectPath, dryRunOptions);
        MigrationResult result = migrator.migrate();
        
        // Then
        assertThat(result).isNotNull();
        assertThat(migrator.getContext().isDryRun()).isTrue();
    }
    
    @Test
    void shouldSkipSpecifiedSteps() throws IOException {
        // Given
        Files.createFile(testProjectPath.resolve("pom.xml"));
        
        MigrationOptions skipOptions = MigrationOptions.builder()
                .dryRun(true)
                .skipSteps(java.util.Set.of("test_execution", "runtime_validation"))
                .build();
        
        // When
        JavaAutoMigrator migrator = new JavaAutoMigrator(testProjectPath, skipOptions);
        MigrationResult result = migrator.migrate();
        
        // Then
        assertThat(result).isNotNull();
        assertThat(migrator.getContext().getOptions().shouldSkipStep("test_execution")).isTrue();
        assertThat(migrator.getContext().getOptions().shouldSkipStep("runtime_validation")).isTrue();
        assertThat(migrator.getContext().getOptions().shouldSkipStep("project_analysis")).isFalse();
    }
    
    @Test
    void shouldInitializeWithCorrectTargetVersions() throws IOException {
        // Given
        Files.createFile(testProjectPath.resolve("pom.xml"));
        
        // When
        JavaAutoMigrator migrator = new JavaAutoMigrator(testProjectPath, defaultOptions);
        migrator.migrate();
        
        // Then
        assertThat(migrator.getContext().getTargetSpringBootVersion()).isEqualTo("3.2.0");
        assertThat(migrator.getContext().getTargetJavaVersion()).isEqualTo("21");
    }
    
    @Test
    void shouldTrackMigrationStats() throws IOException {
        // Given
        Files.createFile(testProjectPath.resolve("pom.xml"));
        
        // When
        JavaAutoMigrator migrator = new JavaAutoMigrator(testProjectPath, defaultOptions);
        MigrationResult result = migrator.migrate();
        
        // Then
        assertThat(result.getStats()).isNotNull();
        assertThat(result.getStats().getStartTime()).isNotNull();
        assertThat(result.getStats().getEndTime()).isNotNull();
        assertThat(result.getTotalDuration()).isNotNull();
    }
}
